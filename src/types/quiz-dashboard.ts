import type { ItemBlock, Option, Question, HeaderBody, ImageBody } from './models';

export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: ChartData;
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

// ใช้โครงสร้างตาม models.ts แต่เพิ่ม user response data
export interface ParticipantDetails {
  submissionId: number;
  assessmentId: number;
  assessmentName: string;
  userId: number;
  userName: string;
  startTime: string;
  endTime: string;
  totalScore: number;
  maxScore: number;
  scorePercentage: number;
  itemBlocks: ItemBlockWithUserData[];
}

// Extend ItemBlock จาก models.ts โดยเพิ่ม user response data
export interface ItemBlockWithUserData extends Omit<ItemBlock, 'questions' | 'options'> {
  questions?: QuestionWithUserData[];
  options?: OptionWithUserData[];
  headerBody?: HeaderBody;
  imageBody?: ImageBody;
}

export interface QuestionWithUserData extends Question {
  userResponse?: {
    id: number;
    selectedOptionId?: number;
    selectedOptionText?: string;
    textAnswer?: string;
    isCorrect: boolean;
    score: number;
    currentScore?: number; // คะแนนที่ admin ให้ไว้แล้วสำหรับ TEXTFIELD
  } | null;
}

export interface OptionWithUserData extends Option {
  isSelected: boolean;
}

// Interface สำหรับข้อมูลคำถามที่ถูก process แล้วใน ParticipantDetailsPage
export interface ProcessedQuestionData {
  questionId: number;
  questionSequence: number;
  questionText: string;
  questionType: string;
  score: number; // คะแนนเต็มของคำถาม
  isCorrect: boolean;
  selectedOptionText?: string;
  textAnswer?: string;
  options: Array<OptionWithUserData>;
  itemBlockId: number;
  itemBlockType: string;
}
